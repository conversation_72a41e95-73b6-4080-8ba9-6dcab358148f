:root {
  /* Maroon Color Palette */
  --primary-color: #5a0017;
  --secondary-color: #3d000f;
  --dark-maroon: #2a000a;
  --light-maroon: #f5e6ea;
  --accent-color: #7a1c2a;
  --highlight: #9e3a4a;
  --text-color: var(--dark-maroon);
  --text-light: #636e72;
  --light-bg: var(--light-maroon);
  --gradient-primary: linear-gradient(135deg, #5a0017 0%, #7a1c2a 100%);
  --gradient-secondary: linear-gradient(135deg, #3d000f 0%, #9e3a4a 100%);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: "Poppins", sans-serif;
  line-height: 1.6;
}

/* Hero Section */
.hero {
  background: var(--gradient-primary);
  color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding: 100px 0;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.08'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.2;
  mix-blend-mode: overlay;
}

.hero h1 {
  font-family: "Playfair Display", serif;
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
}

.hero p {
  font-size: 1.65rem;
  margin-bottom: 3rem;
  opacity: 0.98;
  color: rgba(255, 255, 255, 0.98);
  max-width: 650px;
}

.hero .btn-warning {
  background: var(--accent-color);
  border: none;
  color: white;
  font-weight: 600;
  padding: 18px 40px;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  transform: translateY(0);
}

.hero .btn-warning:hover {
  background: var(--highlight);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hero .btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.7);
  color: white;
  font-weight: 600;
  padding: 18px 40px;
  transition: all 0.3s ease;
}

.hero .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-3px);
}

/* Hero Image Styles */
.hero-image-wrapper {
  position: relative;
  padding: 20px;
  transform: translateY(0);
  transition: transform 0.5s ease-in-out;
}

.hero-image-wrapper:hover {
  transform: translateY(-10px);
}

.hero-image {
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  transform: perspective(1000px) rotateY(-8deg);
  transition: transform 0.6s ease;
}

.hero-image:hover {
  transform: perspective(1000px) rotateY(0deg) scale(1.02);
}

/* Floating Elements */
.floating-element {
  position: absolute;
  color: var(--highlight);
  background: rgba(255, 255, 255, 0.95);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  animation: float 4s ease-in-out infinite;
  font-size: 1.8rem;
}

.floating-1 {
  top: 15%;
  left: 5%;
  animation-delay: 0s;
}

.floating-2 {
  top: 35%;
  right: 10%;
  animation-delay: 1.5s;
}

.floating-3 {
  bottom: 25%;
  left: 15%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-25px) rotate(15deg) scale(1.05);
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  color: white;
  opacity: 0.9;
  animation: bounce 2s infinite cubic-bezier(0.28, 0.84, 0.42, 1);
  transition: all 0.3s ease;
  font-size: 2.5rem;
}

.scroll-indicator:hover {
  opacity: 1;
  transform: translateX(-50%) translateY(-8px);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
  60% {
    transform: translateY(-7px);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 3.5rem;
    text-align: center;
  }
  
  .hero p {
    font-size: 1.3rem;
    text-align: center;
    max-width: 100%;
  }

  .hero .d-flex.gap-3 {
    flex-direction: column;
    align-items: center;
  }

  .hero .btn-warning, .hero .btn-outline-light {
    width: 80%;
    margin-bottom: 15px;
  }

  .hero .mt-5.d-flex {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .hero-image-wrapper {
    margin-top: 3rem;
  }

  .floating-element {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

/* Features Section */
.features-section {
  padding: 80px 0;
  background-color: var(--light-maroon);
}

.feature-card {
  padding: 30px;
  border-radius: 15px;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 20px;
}

.feature-card h3 {
  color: var(--text-color);
  font-size: 1.5rem;
  margin: 1.5rem 0 1rem;
}

.feature-card p {
  color: var(--text-light);
  font-size: 1rem;
  line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
  padding: 80px 0;
  background: var(--light-maroon);
}

.testimonial-card {
  padding: 30px;
  border-radius: 15px;
  background: white;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  margin-bottom: 30px;
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 20px;
}

.testimonial-author {
  font-weight: 600;
  color: var(--primary-color);
}

/* Newsletter Section */
.newsletter-section {
  padding: 80px 0;
  background: var(--gradient-secondary);
  color: white;
}

.newsletter-form .form-control {
  border-radius: 25px 0 0 25px;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.newsletter-form .form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .btn-primary {
  border-radius: 0 25px 25px 0;
  padding: 15px 30px;
  background: white;
  color: var(--primary-color);
}

.newsletter-form .btn-primary:hover {
  background: var(--light-maroon);
}

/* Book Card Styles */
.book-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
}

.book-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.book-card img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.book-card .card-body {
  padding: 1.5rem;
}

.category-label {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--accent-color);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
}

/* Search and Filter */
.search-filter-container {
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  margin-bottom: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .feature-card {
    margin-bottom: 30px;
  }
  
  .testimonial-card {
    margin-bottom: 20px;
  }
}

.cta-button {
  background: var(--accent-color);
  color: white;
  padding: 12px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
}

.cta-button:hover {
  background: #e67e22;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
}

.section-title {
  font-family: "Playfair Display", serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 3rem;
}

.book-card h5 {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.book-card .author {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  font-style: italic;
}

.book-card .price {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.book-card .btn-primary {
  background: var(--gradient-primary);
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.book-card .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
  background: var(--gradient-secondary);
}

/* List View Styles */
.list-view .book-card {
  border-radius: 15px;
  margin-bottom: 1.5rem;
}

.list-view .book-card .row {
  align-items: center;
}

.list-view .book-card img {
  height: 150px;
  width: 100px;
  object-fit: cover;
  border-radius: 10px;
}

.list-view .book-card .card-body {
  background: white;
}

/* Search and Filter Styles */
.filter-tag {
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: var(--primary-color) !important;
  color: white !important;
  border-color: var(--primary-color) !important;
}

.filter-tag:hover {
  transform: translateY(-1px);
}

/* Navbar Styles */
.navbar {
  padding: 1rem 0;
  background: var(--white);
  box-shadow: var(--shadow-sm);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.navbar-brand img {
  height: 40px;
  margin-right: 0.5rem;
}

.nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition);
}

.nav-link:hover {
  color: var(--primary-color);
}

.btn-cart {
  position: relative;
  padding: 0.5rem 1rem;
  color: var(--text-color);
  background: transparent;
  transition: var(--transition);
}

.btn-cart:hover {
  color: var(--primary-color);
}

.btn-cart .badge {
  position: relative;
  top: -8px;
  right: -5px;
  font-size: 0.65rem;
  padding: 0.25rem 0.4rem;
  min-width: 18px;
  height: 18px;
  line-height: 1;
  border-radius: 9px;
  background: var(--primary-color);
  color: var(--white);
  font-weight: 600;
}

.dropdown-menu {
  border: none;
  box-shadow: var(--shadow-md);
  border-radius: 0.5rem;
}

.dropdown-item {
  padding: 0.75rem 1.5rem;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background-color: var(--light-color);
  color: var(--accent-color);
}

.dropdown-item i {
  color: var(--accent-color);
  width: 1.25rem;
}

/* Cart Panel Styles */
.offcanvas {
  border-left: none;
  box-shadow: var(--shadow-lg);
}

.offcanvas-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.offcanvas-title {
  color: var(--text-color);
  font-weight: 600;
}

.offcanvas-title i {
  color: var(--accent-color);
}

.cart-item {
  transition: all 0.3s ease;
}

.cart-item:hover {
  background-color: var(--light-color);
}

.cart-item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 0.5rem;
}

.cart-item-title {
  color: var(--text-color);
  font-weight: 500;
}

.btn-quantity {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  background: var(--light-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.btn-quantity:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.cart-summary {
  background-color: var(--light-color);
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.empty-cart {
  color: var(--text-light);
}

/* Auth Modal Styles */
.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  padding: 1.5rem;
}

.modal-title {
  color: var(--text-color);
  font-weight: 600;
}

.modal-title i {
  color: var(--accent-color);
}

.modal-body {
  padding: 1.5rem;
}

.form-label {
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.input-group-text {
  background-color: var(--light-color);
  border: 1px solid var(--border-color);
  color: var(--accent-color);
}

.form-control {
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.1);
}

.form-check-input:checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

/* Auth Button Styles */
.btn-outline-primary,
.btn-primary {
  min-width: 120px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-outline-primary {
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  color: white;
  background: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary {
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  border: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary i,
.btn-outline-primary i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-md);
    margin-top: 1rem;
  }

  .nav-item {
    margin: 0.5rem 0;
  }

  .btn-cart {
    margin-right: 0;
  }
}

/* Search Styles */
.search-container {
  max-width: 400px;
  margin: 0 auto 3rem;
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
}

/* Modal Styles */
.modal-content {
  border-radius: 15px;
  border: none;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 1rem 0;
}

.quantity-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background: var(--secondary-color);
}

.buy-now-btn,
.add-to-cart-btn {
  border: none;
  padding: 12px 25px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-right: 10px;
}

.buy-now-btn {
  background: var(--secondary-color);
  color: white;
}

.add-to-cart-btn {
  background: var(--accent-color);
  color: white;
}

.buy-now-btn:hover,
.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Footer Styles */
.footer {
  background: var(--secondary-color);
  color: white;
  padding: 80px 0 0;
}

.footer-top {
  padding: 80px 0 40px;
  background: linear-gradient(135deg, var(--text-dark), #1a1a1a);
}

.footer-bottom {
  padding: 20px 0;
  background: #1a1a1a;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
}

.footer-description {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 25px;
  line-height: 1.6;
}

.footer h4 {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 10px;
}

.footer h4::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 2px;
  background: var(--accent-color);
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: 12px;
}

.footer-links ul li a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.footer-links ul li a i {
  font-size: 0.8rem;
  margin-right: 8px;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.footer-links ul li a:hover {
  color: #fff;
  padding-left: 5px;
}

.footer-links ul li a:hover i {
  color: #fff;
}

.footer-contact p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.footer-contact p i {
  color: var(--accent-color);
  margin-right: 10px;
  font-size: 1.1rem;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--accent-color);
  color: #fff;
  transform: translateY(-3px);
}

.footer-newsletter {
  margin-top: 25px;
}

.footer-newsletter form {
  display: flex;
  gap: 10px;
}

.footer-newsletter input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  border-radius: 5px;
}

.footer-newsletter input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.footer-newsletter button {
  padding: 10px 20px;
  background: var(--accent-color);
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.footer-newsletter button:hover {
  background: var(--primary-color);
}

.copyright {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
}

.footer-bottom-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-bottom-links a:hover {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .footer-top {
    padding: 40px 0 20px;
  }
  
  .footer-bottom-links {
    justify-content: center;
    margin-top: 15px;
  }
  
  .copyright {
    text-align: center;
  }
  
  .footer-newsletter form {
    flex-direction: column;
  }
  
  .footer-newsletter button {
    width: 100%;
  }
}

/* Contact Page Styles */
.contact-hero {
  padding: 100px 0;
  background: var(--gradient-primary);
  color: white;
}

.contact-hero h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.contact-hero .lead {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.contact-stats {
  display: flex;
  gap: 2rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--highlight);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.contact-hero-image img {
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Contact Info Section */
.contact-info-section {
  padding: 80px 0;
  background-color: var(--light-maroon);
}

.contact-info-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
  transition: transform 0.3s ease;
}

.contact-info-card:hover {
  transform: translateY(-5px);
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.icon-wrapper i {
  font-size: 1.5rem;
  color: white;
}

.contact-info-card h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.contact-info-card p {
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.direction-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.direction-link:hover {
  color: var(--accent-color);
}

/* Contact Form Section */
.contact-form-section {
  padding: 80px 0;
  background-color: white;
}

.contact-form-wrapper {
  padding: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-description {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  border: 1px solid #e0e0e0;
  padding: 0.8rem 1rem;
  border-radius: 5px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: none;
}

.map-wrapper {
  position: relative;
  height: 100%;
  min-height: 400px;
  border-radius: 10px;
  overflow: hidden;
}

.map-container {
  height: 100%;
}

.map-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 2rem;
  color: white;
}

.overlay-content h4 {
  margin-bottom: 0.5rem;
}

.overlay-content p {
  margin-bottom: 1rem;
  opacity: 0.9;
}

/* FAQ Section */
.faq-section {
  padding: 80px 0;
  background-color: var(--light-maroon);
}

.accordion-item {
  border: none;
  margin-bottom: 1rem;
  background: white;
  border-radius: 8px !important;
  overflow: hidden;
}

.accordion-button {
  padding: 1.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.accordion-button:not(.collapsed) {
  background-color: var(--primary-color);
  color: white;
}

.accordion-button:focus {
  box-shadow: none;
  border-color: transparent;
}

.accordion-body {
  padding: 1.5rem;
  color: var(--text-light);
}

/* Social Section */
.social-section {
  padding: 80px 0;
  background: var(--gradient-secondary);
  color: white;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2rem;
}

.social-link {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: white;
  color: var(--primary-color);
  transform: translateY(-3px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .contact-hero {
    padding: 60px 0;
  }

  .contact-hero h1 {
    font-size: 2.5rem;
  }

  .contact-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .contact-hero-image {
    margin-top: 2rem;
  }

  .map-wrapper {
    margin-top: 2rem;
    min-height: 300px;
  }

  .social-links {
    flex-wrap: wrap;
  }
}

/* About Page Styles */
.about-hero {
  padding: 100px 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.stat-box {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--accent-color);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.mission-section {
  padding: 100px 0;
  background-color: #f8f9fa;
}

.mission-content {
  padding: 30px;
}

.mission-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.value-item {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.value-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.value-item i {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 15px;
}

.value-item h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: var(--text-dark);
}

.value-item p {
  color: var(--text-light);
  margin: 0;
}

.team-section {
  padding: 100px 0;
  background: white;
}

.team-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.team-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.team-image {
  position: relative;
  overflow: hidden;
}

.team-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.team-card:hover .team-image img {
  transform: scale(1.1);
}

.team-info {
  padding: 20px;
  text-align: center;
}

.team-info h4 {
  margin-bottom: 5px;
  color: var(--text-dark);
}

.team-info .position {
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 10px;
}

.team-info .bio {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.team-info .social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.team-info .social-links a {
  color: var(--text-light);
  transition: all 0.3s ease;
}

.team-info .social-links a:hover {
  color: var(--accent-color);
}

.timeline-section {
  padding: 80px 0;
  background-color: var(--light-bg);
}

.timeline {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 0;
}

.timeline::after {
  content: '';
  position: absolute;
  width: 4px;
  background: var(--primary-color);
  top: 0;
  bottom: 0;
  left: 50%;
  margin-left: -2px;
  border-radius: 2px;
}

.timeline-item {
  padding: 10px 40px;
  position: relative;
  width: 50%;
  box-sizing: border-box;
}

.timeline-item:nth-child(odd) {
  left: 0;
}

.timeline-item:nth-child(even) {
  left: 50%;
}

.timeline-content {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.timeline-year {
  position: absolute;
  top: -15px;
  left: 20px;
  background: var(--primary-color);
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: bold;
}

.timeline-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 50%;
  top: 20px;
  right: -60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 1;
}

.timeline-item:nth-child(even) .timeline-icon {
  left: -60px;
}

/* Awards Section */
.awards-section {
  padding: 80px 0;
  background: white;
}

.award-card {
  text-align: center;
  padding: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.award-card:hover {
  transform: translateY(-5px);
}

.award-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.award-card h3 {
  color: var(--text-color);
  margin-bottom: 15px;
}

/* Testimonials Section */
.testimonial-card {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.testimonial-content {
  position: relative;
  margin-bottom: 20px;
}

.testimonial-content i {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.testimonial-content p {
  font-style: italic;
  color: var(--text-color);
  line-height: 1.6;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  margin: 0;
  color: var(--text-color);
}

.author-info p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* Partners Section */
.partners-section {
  padding: 80px 0;
  background: white;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  align-items: center;
  justify-items: center;
}

.partner-logo {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.partner-logo:hover {
  transform: scale(1.05);
}

.partner-logo img {
  max-width: 100%;
  height: auto;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partner-logo:hover img {
  filter: grayscale(0%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .timeline::after {
    left: 31px;
  }

  .timeline-item {
    width: 100%;
    padding-left: 70px;
    padding-right: 25px;
  }

  .timeline-item:nth-child(even) {
    left: 0;
  }

  .timeline-icon {
    left: 10px !important;
    right: auto !important;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Admin Dashboard Styles */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.bg-dark {
    background-color: #212529 !important;
}

.min-vh-100 {
    min-height: 100vh !important;
}

/* Sidebar Styles */
.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.modal-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Button Styles */
.btn-primary {
    background-color: var(--gradient-primary);
    border-color: var(--gradient-primary);
}

.btn-primary:hover {
    background-color: var(--gradient-secondary);
    border-color: var(--gradient-secondary);
}

.btn-secondary {
    background-color: var(--gradient-secondary);
    background-color: #858796;
    border-color: #858796;
}

.btn-secondary:hover {
    background-color: #717384;
    border-color: #6b6d7d;
}

/* Form Styles */
.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 500;
    color: #5a5c69;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100% !important;
        height: auto !important;
        position: relative !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* New Blog Section */
.blog-section {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.blog-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.blog-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.blog-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.blog-card .card-body {
  padding: 1.5rem;
}

.blog-card .card-title {
  color: var(--primary-color);
  font-weight: 600;
}

.blog-card .card-text {
  color: var(--text-light);
}

.blog-card .btn {
  background: var(--gradient-primary);
  border: none;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.blog-card .btn:hover {
  background: var(--gradient-secondary);
  transform: translateY(-2px);
}

/* Profile Page Styles */
.profile-header {
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  color: white;
  margin-top: 76px;
}

.profile-avatar {
  background: rgba(255, 255, 255, 0.1);
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.profile-avatar i {
  color: white;
}

.profile-name {
  font-family: 'Playfair Display', serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

.profile-email {
  font-size: 1.1rem;
  opacity: 0.9;
}

.profile-stats {
  margin-top: 2rem;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  text-align: center;
}

.stat-item h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

/* Profile Navigation */
.profile-nav .nav-link {
  color: var(--text-color);
  padding: 1rem 1.5rem;
  border-radius: 0;
  transition: all 0.3s ease;
}

.profile-nav .nav-link:hover {
  background-color: var(--light-color);
  color: var(--accent-color);
}

.profile-nav .nav-link.active {
  background-color: var(--primary-color);
  color: white;
}

.profile-nav .nav-link i {
  width: 1.5rem;
  text-align: center;
}

/* Order Card Styles */
.order-card {
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.order-card:hover {
  box-shadow: var(--shadow-md);
}

.order-header {
  background-color: var(--light-color);
  border-bottom: 1px solid var(--border-color);
}

.order-details {
  background-color: white;
}

.order-info-card {
  background-color: var(--light-color);
  padding: 1.25rem;
  border-radius: 0.5rem;
  height: 100%;
}

.order-info-card h6 {
  font-size: 0.9rem;
  font-weight: 600;
}

.order-info-card p {
  font-size: 0.95rem;
}

.order-info-card i {
  color: var(--accent-color);
}

.order-items {
  margin-top: 1.5rem;
}

.order-item {
  background-color: var(--light-color);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.order-item:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.order-item-image {
  width: 80px;
  height: 120px;
  object-fit: cover;
  border-radius: 0.25rem;
  box-shadow: var(--shadow-sm);
}

.order-actions {
  border-top: 1px solid var(--border-color);
  padding-top: 1.5rem;
}

.order-actions .btn {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.order-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.order-actions .btn i {
  font-size: 0.9rem;
}

/* Status Badge Styles */
.badge {
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 0.85rem;
}

.badge.bg-success {
  background-color: var(--success-color) !important;
}

.badge.bg-info {
  background-color: var(--info-color) !important;
}

.badge.bg-secondary {
  background-color: var(--secondary-color) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .order-info-card {
    margin-bottom: 1rem;
  }

  .order-item {
    flex-direction: column;
    text-align: center;
  }

  .order-item-image {
    margin: 0 auto 1rem;
  }

  .order-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .order-actions .btn {
    width: 100%;
  }
}

/* Filters Sidebar Styles */
.filters-sidebar {
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 2rem;
}

.filters-sidebar h4 {
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.filters-sidebar .form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.filters-sidebar .input-group {
  margin-bottom: 1rem;
}

.filters-sidebar .form-select {
  border-color: #ced4da;
}

.filters-sidebar .btn-outline-secondary {
  margin-top: 1rem;
  border-color: #6c757d;
}

.filters-sidebar .btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

/* Adjust book grid for new layout */
.book-card {
  height: 100%;
  transition: transform 0.2s ease-in-out;
}

.book-card:hover {
  transform: translateY(-5px);
}

/* Book Modal Styles */
.modal-content {
  border: none;
  border-radius: 12px;
  overflow: hidden;
}

.modal-header {
  padding: 1rem 1.5rem;
}

.modal-body {
  padding: 0 1.5rem 1.5rem;
}

.book-image-container {
  text-align: center;
}

.book-image-container img {
  max-height: 400px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.book-image-container img:hover {
  transform: scale(1.02);
}

.book-actions {
  margin-top: 1rem;
}

.book-actions .btn {
  padding: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.book-actions .btn-outline-primary:hover {
  background-color: #e3f2fd;
}

.book-details {
  padding-left: 1rem;
}

.book-details h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
}

.book-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.book-meta .badge {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

.rating {
  display: flex;
  align-items: center;
}

.rating i {
  font-size: 0.875rem;
}

.price-section h3 {
  font-size: 1.75rem;
  font-weight: 600;
}

.quantity-selector {
  max-width: 150px;
}

.quantity-selector .btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-selector input {
  text-align: center;
  font-weight: 500;
}

.book-description {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #dee2e6;
}

.book-description h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.book-description p {
  line-height: 1.6;
}

/* Animation for modal */
.modal.fade .modal-dialog {
  transform: scale(0.95);
  transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
  transform: scale(1);
}
