import express from "express";
import cookieParser from "cookie-parser";
import "dotenv/config";
import connectDb from "./config/db.js";
import booksRouter from "./routes/book.js";
import ordersRouter from "./routes/order.js";
import usersRouter from "./routes/user.js";
import frontRouter from "./routes/frontend.js";
import chatbotRouter from "./routes/chatbot.js";
import dashboardRouter from "./routes/dashboard.js";
//salmaa
const app = express();
const port = 3001;
// 1. Basic Setup 
app.use(express.json());                    //  Helps read JSON data 
app.use(cookieParser());                    // Helps manage cookies 
app.use(express.urlencoded({ extended: true })); // Helps read form data
app.use(express.static("public"));          // images, CSS, JavaScript
app.set("view engine", "ejs");              // Tells the app to use EJS for making web pages

// 2. Routes 
app.use("/api/book", booksRouter);          // Handles all book-related stuff (add, edit, delete books)
app.use("/api/order", ordersRouter);        // Handles all order-related stuff (shopping cart, checkout)
app.use("/api/user", usersRouter);          // Handles all user-related stuff (login, register, profile)
app.use("/api/chatbot", chatbotRouter);     // Handles the chatbot feature
app.use("/api/dashboard", dashboardRouter); // Handles the admin dashboard
app.use(frontRouter);                       // Handles the main website pages

// 3. 404 Handler 
app.use((req, res, next) => {
  res.render("404");                        // Shows the 404 page when someone tries to visit a page that doesn't exist
});

connectDb();

app.listen(port, () => {
  console.log(`Example app listening on port ${port}`);
});
