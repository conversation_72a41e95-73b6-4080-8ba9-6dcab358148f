<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Page Not Found - BookHouse</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="/css/main.css" />
    <style>
      .error-page {
        min-height: 100vh;
        display: flex;
        align-items: center;
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        padding: 2rem 0;
      }

      .error-content {
        text-align: center;
        color: var(--white);
        padding: 2rem;
      }

      .error-code {
        font-size: 8rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        animation: float 3s ease-in-out infinite;
      }

      .error-title {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .error-message {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        opacity: 0.9;
      }

      .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-bottom: 3rem;
      }

      .error-actions .btn {
        padding: 0.8rem 2rem;
        font-weight: 500;
        border-radius: 50px;
        transition: all 0.3s ease;
      }

      .error-actions .btn-primary {
        background: var(--white);
        color: var(--primary-color);
        border: none;
      }

      .error-actions .btn-outline-light:hover {
        background: var(--white);
        color: var(--primary-color);
      }

      .error-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
      }

      .feature-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 2rem;
        transition: transform 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-5px);
      }

      .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--white);
      }

      .feature-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }

      .feature-text {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      @keyframes float {
        0% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-20px);
        }
        100% {
          transform: translateY(0px);
        }
      }

      @media (max-width: 768px) {
        .error-code {
          font-size: 6rem;
        }

        .error-title {
          font-size: 2rem;
        }

        .error-actions {
          flex-direction: column;
        }

        .error-actions .btn {
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="error-page">
      <div class="container">
        <div class="error-content text-white">
          <div class="error-code">404</div>
          <h1 class="error-title">Page Not Found</h1>
          <p class="error-message">
            Oops! The page you're looking for seems to have wandered off into
            the library stacks.
          </p>
          <div class="error-actions">
            <a href="/" class="btn btn-outline-light">
              <i class="fas fa-home me-2"></i>Back to Home
            </a>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
